/* ===== КОМПОНЕНТЫ ИНТЕРФЕЙСА ===== */

/* ===== КНОПКИ ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    font-weight: 500;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
}

.btn-primary {
    background: var(--gradient-gold);
    color: var(--primary-black);
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-bright-gold) 0%, var(--accent-gold) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-gold);
    color: var(--primary-black);
}

.btn-secondary {
    background: transparent;
    color: var(--text-white);
    border: 2px solid var(--accent-gold);
}

.btn-secondary:hover {
    background: var(--accent-gold);
    color: var(--primary-black);
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success-green);
    color: var(--text-white);
}

.btn-success:hover {
    background: #16a34a;
    transform: translateY(-2px);
    color: var(--text-white);
}

.btn-danger {
    background: var(--danger-red);
    color: var(--text-white);
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
    color: var(--text-white);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
}

/* ===== КАРТОЧКИ ===== */
.card {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--eco-bright-green);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--primary-light-green-100);
    background: var(--primary-light-green);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--primary-light-green-100);
    background: var(--primary-light-green-50);
}

.card-title {
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 600;
}

.card-subtitle {
    color: var(--text-green-medium);
    font-size: var(--font-size-sm);
    margin-bottom: 1rem;
}

.card-text {
    color: var(--text-dark-medium);
    margin-bottom: 1rem;
}

/* ===== ФОРМЫ ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-white);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-white);
    background: rgba(26, 26, 26, 0.8);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    backdrop-filter: blur(5px);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
    background: rgba(26, 26, 26, 0.9);
}

.form-control::placeholder {
    color: var(--text-medium-gray);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23d4af37' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    accent-color: var(--accent-gold);
}

.form-check-label {
    color: var(--text-light-gray);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

/* ===== АЛЕРТЫ ===== */
.alert {
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius-md);
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border-left-color: var(--success-green);
    color: #86efac;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: var(--warning-yellow);
    color: #fcd34d;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: var(--danger-red);
    color: #fca5a5;
}

.alert-info {
    background: rgba(22, 163, 74, 0.1);
    border-left-color: var(--info-green);
    color: #86efac;
}

.alert-dismissible {
    position: relative;
    padding-right: 3rem;
}

.btn-close {
    position: absolute;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: inherit;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition-fast);
}

.btn-close:hover {
    opacity: 1;
}

/* ===== ПРОГРЕСС БАРЫ ===== */
.progress {
    height: 0.75rem;
    background: rgba(26, 26, 26, 0.8);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    background: var(--gradient-gold);
    border-radius: var(--border-radius-sm);
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 35%, rgba(255, 255, 255, 0.2) 35%, rgba(255, 255, 255, 0.2) 65%, transparent 65%);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}
