/* ===== СОВРЕМЕННЫЙ FOOTER ===== */

.modern-footer {
    background: var(--gradient-footer);
    color: var(--text-white);
    padding: 3rem 0 1rem;
    position: relative;
    overflow: hidden;
}

.modern-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--eco-pattern-bg);
    opacity: 0.1;
    pointer-events: none;
}

.footer-content {
    position: relative;
    z-index: 1;
}

/* Заголовки секций */
.footer-section-title {
    color: var(--text-white);
    font-weight: 700;
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--gradient-eco-bright);
    border-radius: 1px;
}

/* Описание компании */
.company-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 0.95rem;
}

/* Социальные ссылки */
.social-links-modern {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-link-modern {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: var(--text-white);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-link-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-eco-bright);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.social-link-modern:hover::before {
    opacity: 1;
}

.social-link-modern:hover {
    color: white;
    text-decoration: none;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.social-link-modern .social-icon {
    width: 20px;
    height: 20px;
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.social-link-modern:hover .social-icon {
    transform: scale(1.1);
}

/* Специальные цвета для социальных сетей */
.social-link-modern.facebook:hover {
    box-shadow: 0 8px 25px rgba(59, 89, 152, 0.4);
}

.social-link-modern.facebook::before {
    background: #3b5998;
}

.social-link-modern.twitter:hover {
    box-shadow: 0 8px 25px rgba(29, 161, 242, 0.4);
}

.social-link-modern.twitter::before {
    background: #1da1f2;
}

.social-link-modern.linkedin:hover {
    box-shadow: 0 8px 25px rgba(0, 119, 181, 0.4);
}

.social-link-modern.linkedin::before {
    background: #0077b5;
}

.social-link-modern.telegram:hover {
    box-shadow: 0 8px 25px rgba(0, 136, 204, 0.4);
}

.social-link-modern.telegram::before {
    background: #0088cc;
}

/* Ссылки в футере */
.footer-links-modern {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links-modern li {
    margin-bottom: 0.75rem;
}

.footer-links-modern a {
    color: var(--text-light);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 1rem;
}

.footer-links-modern a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: var(--eco-bright-green);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.footer-links-modern a:hover {
    color: var(--text-white);
    text-decoration: none;
    padding-left: 1.25rem;
}

.footer-links-modern a:hover::before {
    background: var(--eco-lime-green);
    transform: translateY(-50%) scale(1.5);
}

/* Контактная информация */
.contact-info-modern {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.contact-icon {
    width: 18px;
    height: 18px;
    color: var(--eco-bright-green);
    flex-shrink: 0;
}

/* Статистика в футере */
.footer-stats-modern {
    display: flex;
    gap: 2rem;
    align-items: center;
    flex-wrap: wrap;
}

.stat-item-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.stat-icon-modern {
    width: 16px;
    height: 16px;
    color: var(--eco-bright-green);
}

.stat-value-modern {
    color: var(--text-white);
    font-weight: 600;
    font-size: 1rem;
}

/* Разделитель */
.footer-divider-modern {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    margin: 2rem 0 1.5rem;
}

/* Копирайт */
.copyright-modern {
    color: var(--text-light);
    font-size: 0.875rem;
    margin: 0;
}

/* Партнеры */
.partners-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.partners-grid {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.partner-logo {
    width: 120px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.partner-logo:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
}

/* Анимации */
@keyframes footerGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
    }
    50% {
        box-shadow: 0 0 30px rgba(16, 185, 129, 0.4);
    }
}

.footer-glow {
    animation: footerGlow 3s ease-in-out infinite;
}

/* Адаптивность */
@media (max-width: 767.98px) {
    .modern-footer {
        padding: 2rem 0 1rem;
    }
    
    .footer-stats-modern {
        gap: 1rem;
        justify-content: center;
    }
    
    .social-links-modern {
        justify-content: center;
    }
    
    .partners-grid {
        gap: 1rem;
    }
    
    .partner-logo {
        width: 100px;
        height: 50px;
        font-size: 0.7rem;
    }
}

@media (max-width: 575.98px) {
    .footer-stats-modern {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }
    
    .social-links-modern {
        gap: 0.75rem;
    }
    
    .social-link-modern {
        width: 40px;
        height: 40px;
    }
    
    .social-link-modern .social-icon {
        width: 18px;
        height: 18px;
    }
}
