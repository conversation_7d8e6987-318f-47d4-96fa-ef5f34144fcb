/* ===== ФИНАЛЬНАЯ ЗЕЛЕНО-ЗОЛОТАЯ ТЕМА GREENCHAIN ECOFUND ===== */

/* ===== ОБНОВЛЕННЫЕ CSS ПЕРЕМЕННЫЕ ===== */
:root {
    /* Золотые акценты для логотипа и названий */
    --brand-gold-primary: #d4af37;
    --brand-gold-light: #ffd700;
    --brand-gold-dark: #b8941f;
    --brand-gold-darker: #9c7e1a;
    
    /* Зеленые градиенты для фонов */
    --bg-gradient-main: linear-gradient(180deg, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%);
    --bg-gradient-section: linear-gradient(180deg, #ecfccb 0%, #d9f99d 50%, #bef264 100%);
    --bg-gradient-dark: linear-gradient(180deg, #166534 0%, #15803d 50%, #16a34a 100%);
    
    /* <PERSON><PERSON><PERSON>екты свечения */
    --glow-gold: 0 0 20px rgba(212, 175, 55, 0.4);
    --glow-gold-strong: 0 0 30px rgba(212, 175, 55, 0.6);
    --glow-green: 0 0 20px rgba(22, 163, 74, 0.4);
    --glow-green-strong: 0 0 30px rgba(22, 163, 74, 0.6);
    
    /* Текстовые тени для свечения */
    --text-glow-gold: 0 0 10px rgba(212, 175, 55, 0.5);
    --text-glow-gold-strong: 0 0 15px rgba(212, 175, 55, 0.7);
    --text-glow-green: 0 0 10px rgba(22, 163, 74, 0.5);
    
    /* Анимации */
    --animation-glow: glowPulse 3s ease-in-out infinite;
    --animation-float: floatEffect 4s ease-in-out infinite;
    --animation-shimmer: shimmerEffect 2s linear infinite;
}

/* ===== ГЛОБАЛЬНЫЕ ФОНОВЫЕ СТИЛИ ===== */
body {
    background: linear-gradient(180deg, #166534 0%, #15803d 25%, #16a34a 50%, #22c55e 75%, #bbf7d0 100%) !important;
    background-attachment: fixed !important;
    min-height: 100vh;
}

.main-content {
    background: transparent;
}

/* Секции с прозрачным фоном для единого градиента */
.section-green-bg,
.content-section-modern {
    background: transparent !important;
    position: relative;
}

/* Убираем дополнительные фоновые эффекты */
.section-green-bg::before {
    display: none;
}

/* ===== СТИЛИЗАЦИЯ ЛОГОТИПА И НАЗВАНИЙ ===== */

/* Основные селекторы для логотипа */
.brand-text-modern,
.brand-text,
.navbar-brand .brand-text {
    color: var(--brand-gold-primary) !important;
    text-shadow: var(--text-glow-gold) !important;
    animation: var(--animation-glow) !important;
    font-weight: 700 !important;
    background: var(--gradient-eco-gold) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* Footer названия */
.footer-section-title {
    color: var(--brand-gold-primary) !important;
    text-shadow: var(--text-glow-gold) !important;
    animation: var(--animation-glow) !important;
}

/* Copyright с названием */
.copyright-modern {
    color: var(--text-light) !important;
}

.copyright-modern .text-eco-bright {
    color: var(--brand-gold-light) !important;
    text-shadow: var(--text-glow-gold) !important;
}

/* Hero заголовки с названием */
.hero-title-modern {
    color: var(--text-white) !important;
}

.hero-title-gradient {
    color: #84cc16 !important;
    text-shadow: 0 0 10px rgba(132, 204, 22, 0.5) !important;
    font-weight: 700 !important;
}

/* Подзаголовок Hero секции */
.hero-subtitle-modern {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    font-weight: 400 !important;
    line-height: 1.6 !important;
}

/* Заголовки страниц */
h1, h2, h3 {
    color: var(--text-dark) !important;
}

/* Специальные классы для золотого текста */
.text-brand-gold {
    color: var(--brand-gold-primary) !important;
    text-shadow: var(--text-glow-gold) !important;
    animation: var(--animation-glow) !important;
    font-weight: 700 !important;
}

/* Анимация свечения для логотипа */
@keyframes glowPulse {
    0%, 100% {
        text-shadow: var(--text-glow-gold);
        color: var(--brand-gold-primary);
    }
    50% {
        text-shadow: var(--text-glow-gold-strong);
        color: var(--brand-gold-light);
    }
}

/* ===== УНИФИКАЦИЯ НАВИГАЦИИ ===== */
.nav-button,
.modern-nav .nav-button,
.auth-nav .nav-button {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    padding: 0.5rem 1rem !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 0.75rem !important;
    color: var(--text-white) !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    min-height: 40px !important;
    min-width: 100px !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Убираем специальные эффекты свечения */
.nav-button.glow,
.nav-button.referrals {
    box-shadow: none !important;
}

.nav-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-button:hover::before {
    left: 100%;
}

.nav-button:hover {
    color: var(--text-white) !important;
    text-decoration: none !important;
    transform: translateY(-2px) !important;
    border-color: var(--eco-bright-green) !important;
    background: rgba(16, 185, 129, 0.2) !important;
    /* Убираем эффекты свечения */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* ===== СТИЛИЗАЦИЯ КОНТЕНТНЫХ СЕКЦИЙ ===== */
.content-section-modern,
.section-modern {
    background: var(--bg-gradient-section) !important;
    padding: 5rem 0 !important;
    position: relative !important;
    margin: 2rem 0 !important;
    border-radius: var(--border-radius-xl) !important;
}

.content-section-modern .container {
    position: relative;
    z-index: 2;
}

/* Заголовки секций с золотым цветом */
.section-title-modern,
.section-title-eco {
    color: var(--brand-gold-primary) !important;
    text-shadow: var(--text-glow-gold) !important;
    animation: var(--animation-glow) !important;
    font-weight: 700 !important;
    text-align: center !important;
    margin-bottom: 1rem !important;
}

.section-subtitle-modern {
    color: var(--text-dark) !important;
    text-align: center !important;
    margin-bottom: 3rem !important;
    font-weight: 500 !important;
}

/* Карточки на светлом фоне */
.feature-card-modern,
.stats-card-modern,
.investment-card {
    background: var(--primary-white) !important;
    border: 1px solid var(--primary-light-green-100) !important;
    border-radius: var(--border-radius-xl) !important;
    box-shadow: var(--shadow-lg) !important;
    transition: all 0.3s ease !important;
}

.feature-card-modern:hover,
.stats-card-modern:hover,
.investment-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: var(--glow-green) !important;
    border-color: var(--eco-bright-green) !important;
}

/* ===== РАЗДЕЛИТЕЛИ СЕКЦИЙ ===== */
.section-divider {
    height: 4px;
    background: linear-gradient(90deg, transparent, var(--eco-bright-green), transparent);
    margin: 3rem 0;
    border-radius: 2px;
    animation: var(--animation-shimmer);
}

@keyframes shimmerEffect {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* ===== СОЦИАЛЬНЫЕ СЕТИ В FOOTER ===== */
.social-link-modern {
    transition: all 0.3s ease !important;
    position: relative !important;
}

.social-link-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-eco-gold);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.social-link-modern:hover::before {
    opacity: 1;
}

.social-link-modern:hover {
    transform: translateY(-3px) scale(1.1) !important;
    box-shadow: var(--glow-gold) !important;
    animation: socialGlow 0.6s ease !important;
}

@keyframes socialGlow {
    0%, 100% {
        box-shadow: var(--glow-gold);
    }
    50% {
        box-shadow: var(--glow-gold-strong);
    }
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ ЭКО-АНИМАЦИИ ===== */
.eco-pulse {
    animation: ecoPulse 2s ease-in-out infinite;
}

@keyframes ecoPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

.eco-float {
    animation: var(--animation-float);
}

@keyframes floatEffect {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.eco-leaf-animation {
    position: relative;
}

.eco-leaf-animation::after {
    content: '🌱';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.2rem;
    animation: leafFloat 4s ease-in-out infinite;
}

@keyframes leafFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-8px) rotate(5deg);
    }
    66% {
        transform: translateY(4px) rotate(-3deg);
    }
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ ЭКО-АНИМАЦИИ ===== */

/* Энергетические частицы - ОТКЛЮЧЕНО */
.energy-particles {
    /* Отключаем фоновые анимации */
}

.energy-particles::before,
.energy-particles::after {
    display: none;
}

@keyframes energyFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-15px) translateX(10px) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-5px) translateX(-5px) scale(0.8);
        opacity: 0.5;
    }
    75% {
        transform: translateY(-20px) translateX(15px) scale(1.1);
        opacity: 0.9;
    }
}

/* Плавающие листья - ОТКЛЮЧЕНО */
.floating-leaves {
    /* Отключаем фоновые анимации */
}

.floating-leaves::before,
.floating-leaves::after {
    display: none;
}

@keyframes leavesFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-25px) rotate(270deg);
        opacity: 0.9;
    }
}

/* Градиентная пульсация */
.gradient-pulse {
    animation: gradientPulse 4s ease-in-out infinite;
}

@keyframes gradientPulse {
    0%, 100% {
        background: var(--bg-gradient-section);
    }
    50% {
        background: linear-gradient(180deg, #dcfce7 0%, #bbf7d0 50%, #86efac 100%);
    }
}

/* Эко-свечение для текста */
.eco-text-glow {
    animation: ecoTextGlow 3s ease-in-out infinite;
}

@keyframes ecoTextGlow {
    0%, 100% {
        text-shadow: var(--text-glow-green);
        color: var(--eco-bright-green);
    }
    50% {
        text-shadow: var(--text-glow-gold);
        color: var(--brand-gold-light);
    }
}

/* Волновой эффект - ОТКЛЮЧЕНО */
.wave-effect {
    /* Отключаем фоновые анимации */
}

.wave-effect::before {
    display: none;
}

@keyframes waveMove {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Природная анимация роста */
.nature-grow {
    animation: natureGrow 2s ease-out;
}

@keyframes natureGrow {
    0% {
        transform: scale(0.8) translateY(20px);
        opacity: 0;
    }
    50% {
        transform: scale(1.05) translateY(-5px);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Золотое мерцание */
.gold-sparkle {
    position: relative;
}

.gold-sparkle::after {
    content: '✨';
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 0.8rem;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0.5) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
}

/* ===== АДАПТИВНОСТЬ ===== */
@media (max-width: 768px) {
    .content-section-modern {
        padding: 3rem 0 !important;
        margin: 1rem 0 !important;
    }
    
    .section-title-modern {
        font-size: 2rem !important;
    }
    
    .nav-button {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.8rem !important;
    }
}

@media (max-width: 576px) {
    .section-title-modern {
        font-size: 1.5rem !important;
    }
    
    .content-section-modern {
        padding: 2rem 0 !important;
        border-radius: var(--border-radius-lg) !important;
    }
}

/* ===== ПРОИЗВОДИТЕЛЬНОСТЬ АНИМАЦИЙ ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
