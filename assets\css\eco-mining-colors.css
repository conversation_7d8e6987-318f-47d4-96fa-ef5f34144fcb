/* ===== ЭКО-МАЙНИНГ ЦВЕТОВАЯ ОПТИМИЗАЦИЯ ===== */

/* Применение новых цветов к существующим элементам */

/* ===== НАВИГАЦИЯ ===== */
.navbar-brand .brand-text {
    background: var(--gradient-eco-bright);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.nav-link:hover {
    color: var(--eco-bright-green) !important;
}

/* ===== HERO СЕКЦИЯ ===== */
.hero-title .text-gradient {
    background: var(--gradient-nature) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.hero-badge {
    background: var(--primary-light-green);
    color: var(--eco-forest-green);
    border: 1px solid var(--eco-bright-green);
}

.stat-number {
    color: var(--eco-forest-green) !important;
}

.stat-label {
    color: var(--text-green-medium) !important;
}

/* ===== КНОПКИ ===== */
.btn-primary {
    background: var(--gradient-eco-bright) !important;
    border: none !important;
    color: white !important;
}

.btn-primary:hover {
    background: var(--gradient-eco-lime) !important;
    color: white !important;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--primary-white) !important;
    border: 2px solid var(--eco-bright-green) !important;
    color: var(--eco-bright-green) !important;
}

.btn-secondary:hover {
    background: var(--eco-bright-green) !important;
    color: white !important;
}

/* ===== ИКОНКИ ===== */
.fas, .fab, .far {
    transition: color 0.3s ease;
}

/* Применение цветов к конкретным иконкам */
.fa-seedling, .fa-leaf {
    color: var(--eco-bright-green) !important;
}

.fa-coins, .fa-dollar-sign {
    color: var(--eco-gold) !important;
}

.fa-chart-line, .fa-chart-area {
    color: var(--eco-lime-green) !important;
}

.fa-users, .fa-user-cog {
    color: var(--eco-emerald) !important;
}

.fa-wallet, .fa-money-bill-wave {
    color: var(--eco-forest-green) !important;
}

.fa-bolt, .fa-zap {
    color: var(--eco-gold) !important;
}

.fa-briefcase, .fa-suitcase {
    color: var(--eco-bright-green) !important;
}

.fa-download, .fa-upload {
    color: var(--eco-lime-green) !important;
}

/* ===== СТАТИСТИЧЕСКИЕ ЭЛЕМЕНТЫ ===== */
.stats-value {
    color: var(--eco-forest-green) !important;
}

.stats-change.positive {
    color: var(--eco-bright-green) !important;
}

.stats-change.negative {
    color: var(--danger-red) !important;
}

/* ===== ИНВЕСТИЦИОННЫЕ КАРТОЧКИ ===== */
.investment-card {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
}

.investment-card:hover {
    border-color: var(--eco-bright-green);
    box-shadow: var(--shadow-eco-bright);
}

.investment-title {
    color: var(--eco-forest-green) !important;
}

.investment-amount {
    color: var(--eco-gold) !important;
}

/* ===== ФОРМЫ ===== */
.form-control {
    border: 1px solid var(--primary-light-green-100);
    background: var(--primary-white);
}

.form-control:focus {
    border-color: var(--eco-bright-green);
    box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
}

.form-label {
    color: var(--text-dark) !important;
}

/* ===== ТАБЛИЦЫ ===== */
.table th {
    background: var(--primary-light-green);
    color: var(--text-dark);
    border-color: var(--primary-light-green-100);
}

.table td {
    border-color: var(--primary-light-green-100);
}

.table-striped tbody tr:nth-of-type(odd) {
    background: var(--primary-light-green-50);
}

/* ===== МОДАЛЬНЫЕ ОКНА ===== */
.modal-content {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
}

.modal-header {
    background: var(--primary-light-green);
    border-bottom: 1px solid var(--primary-light-green-100);
}

.modal-title {
    color: var(--eco-forest-green) !important;
}

/* ===== АЛЕРТЫ ===== */
.alert-success {
    background: var(--primary-light-green);
    border-color: var(--eco-bright-green);
    color: var(--eco-forest-green);
}

.alert-info {
    background: var(--primary-light-green-50);
    border-color: var(--eco-emerald);
    color: var(--eco-forest-green);
}

/* ===== ПРОГРЕСС БАРЫ ===== */
.progress {
    background: var(--primary-light-green-100);
}

.progress-bar {
    background: var(--gradient-eco-bright);
}

/* ===== БЕЙДЖИ ===== */
.badge.bg-primary {
    background: var(--eco-bright-green) !important;
}

.badge.bg-success {
    background: var(--eco-forest-green) !important;
}

.badge.bg-warning {
    background: var(--eco-gold) !important;
}

.badge.bg-info {
    background: var(--eco-emerald) !important;
}

/* ===== ПАГИНАЦИЯ ===== */
.page-link {
    color: var(--eco-bright-green);
    border-color: var(--primary-light-green-100);
}

.page-link:hover {
    color: white;
    background: var(--eco-bright-green);
    border-color: var(--eco-bright-green);
}

.page-item.active .page-link {
    background: var(--eco-bright-green);
    border-color: var(--eco-bright-green);
}

/* ===== ДРОПДАУНЫ ===== */
.dropdown-menu {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
}

.dropdown-item:hover {
    background: var(--primary-light-green);
    color: var(--eco-forest-green);
}

/* ===== ТОСТЫ ===== */
.toast {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
}

.toast-header {
    background: var(--primary-light-green);
    color: var(--eco-forest-green);
}

/* ===== СПЕЦИАЛЬНЫЕ ЭФФЕКТЫ ===== */
.hover-eco-glow:hover {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
    transition: box-shadow 0.3s ease;
}

.text-eco-animate {
    animation: ecoTextGlow 3s ease-in-out infinite;
}

@keyframes ecoTextGlow {
    0%, 100% {
        color: var(--eco-bright-green);
    }
    50% {
        color: var(--eco-lime-green);
    }
}

/* ===== АДАПТИВНЫЕ ЦВЕТА ===== */
@media (prefers-color-scheme: dark) {
    .eco-adaptive {
        filter: brightness(1.2);
    }
}
