/* ===== СОВРЕМЕННЫЙ DASHBOARD ===== */

/* ===== ОСНОВНЫЕ СТИЛИ ===== */
.modern-dashboard {
    background: linear-gradient(135deg, var(--primary-white) 0%, var(--primary-light-green-50) 100%);
    min-height: 100vh;
    padding-top: 80px;
}

/* ===== ПРИВЕТСТВИЕ ===== */
.welcome-header-modern {
    background: var(--gradient-eco-bright);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.welcome-header-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--eco-pattern-bg);
    opacity: 0.1;
    pointer-events: none;
}

.welcome-content {
    position: relative;
    z-index: 1;
}

.welcome-title-modern {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.welcome-wave {
    font-size: 2.5rem;
    animation: wave 2s ease-in-out infinite;
}

.welcome-subtitle-modern {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.welcome-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.welcome-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.welcome-stat-icon {
    width: 20px;
    height: 20px;
    opacity: 0.8;
}

/* ===== СТАТИСТИЧЕСКИЕ КАРТОЧКИ ===== */
.stats-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card-modern {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
    border-radius: var(--border-radius-xl);
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-eco-bright);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stats-card-modern:hover::before {
    transform: scaleX(1);
}

.stats-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(16, 185, 129, 0.15);
    border-color: var(--eco-bright-green);
}

.stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stats-icon-modern {
    width: 50px;
    height: 50px;
    background: var(--primary-light-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--eco-forest-green);
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.stats-card-modern:hover .stats-icon-modern {
    background: var(--eco-bright-green);
    color: white;
    transform: scale(1.1);
}

.stats-value-modern {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    background: var(--gradient-eco-bright);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-label-modern {
    color: var(--text-dark-medium);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.stats-change-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.stats-change-modern.positive {
    color: var(--eco-bright-green);
}

.stats-change-modern.negative {
    color: var(--danger-red);
}

.stats-action {
    margin-top: 1rem;
}

.stats-action .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius-md);
}

/* ===== БЫСТРЫЕ ДЕЙСТВИЯ ===== */
.quick-actions-modern {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
    border-radius: var(--border-radius-xl);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.quick-actions-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-action-btn-modern {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    background: var(--primary-light-green-50);
    border: 1px solid var(--primary-light-green-100);
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    color: var(--text-dark);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-action-btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
    transition: left 0.5s;
}

.quick-action-btn-modern:hover::before {
    left: 100%;
}

.quick-action-btn-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
    border-color: var(--eco-bright-green);
    text-decoration: none;
    color: var(--text-dark);
}

.quick-action-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-eco-bright);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
    transition: all 0.3s ease;
}

.quick-action-btn-modern:hover .quick-action-icon {
    transform: scale(1.1) rotate(360deg);
}

.quick-action-text {
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
}

/* ===== ИНВЕСТИЦИОННЫЕ КАРТОЧКИ ===== */
.investments-section-modern {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
    border-radius: var(--border-radius-xl);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.section-header-modern {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-title-dashboard {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.investment-card-modern {
    background: var(--primary-light-green-50);
    border: 1px solid var(--primary-light-green-100);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.investment-card-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
    border-color: var(--eco-bright-green);
}

.investment-header-modern {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.investment-title-modern {
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.investment-type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.investment-amount-modern {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--eco-gold);
    margin-bottom: 1rem;
}

.investment-stats-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.investment-stat {
    text-align: center;
}

.investment-stat-label {
    font-size: 0.75rem;
    color: var(--text-dark-medium);
    margin-bottom: 0.25rem;
}

.investment-stat-value {
    font-weight: 600;
    color: var(--text-dark);
}

/* ===== АНИМАЦИИ ===== */
@keyframes wave {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(20deg);
    }
    75% {
        transform: rotate(-10deg);
    }
}

@keyframes dashboardFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-animate {
    animation: dashboardFadeIn 0.6s ease-out;
}

.dashboard-animate:nth-child(2) {
    animation-delay: 0.1s;
}

.dashboard-animate:nth-child(3) {
    animation-delay: 0.2s;
}

.dashboard-animate:nth-child(4) {
    animation-delay: 0.3s;
}

/* ===== АДАПТИВНОСТЬ ===== */
@media (max-width: 991.98px) {
    .stats-grid-modern {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 767.98px) {
    .modern-dashboard {
        padding-top: 60px;
    }
    
    .welcome-header-modern {
        padding: 1.5rem;
    }
    
    .welcome-title-modern {
        font-size: 1.5rem;
    }
    
    .welcome-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stats-grid-modern {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 575.98px) {
    .welcome-title-modern {
        font-size: 1.25rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .investment-stats-modern {
        grid-template-columns: repeat(2, 1fr);
    }
}
