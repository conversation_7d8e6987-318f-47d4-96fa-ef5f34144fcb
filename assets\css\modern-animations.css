/* ===== СОВРЕМЕННЫЕ АНИМАЦИИ ===== */

/* ===== БАЗОВЫЕ АНИМАЦИИ ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===== ЭКО-АНИМАЦИИ ===== */
@keyframes ecoGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(16, 185, 129, 0.6);
    }
}

@keyframes ecoTextGlow {
    0%, 100% {
        text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
        color: var(--eco-bright-green);
    }
    50% {
        text-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
        color: var(--eco-lime-green);
    }
}

@keyframes leafFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(5deg);
    }
    66% {
        transform: translateY(5px) rotate(-3deg);
    }
}

@keyframes energyPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* ===== ИНТЕРАКТИВНЫЕ АНИМАЦИИ ===== */
@keyframes hoverLift {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(-5px);
    }
}

@keyframes hoverScale {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(1.05);
    }
}

@keyframes buttonPress {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes iconSpin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes iconBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== КЛАССЫ АНИМАЦИЙ ===== */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.8s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.6s ease-out;
}

.animate-bounce-in {
    animation: bounceIn 0.8s ease-out;
}

/* ===== ЭКО-КЛАССЫ ===== */
.eco-glow {
    animation: ecoGlow 3s ease-in-out infinite;
}

.eco-text-glow {
    animation: ecoTextGlow 3s ease-in-out infinite;
}

.leaf-float {
    animation: leafFloat 4s ease-in-out infinite;
}

.energy-pulse {
    animation: energyPulse 2s ease-in-out infinite;
}

.gradient-shift {
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite;
}

.shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200px 100%;
    animation: shimmer 2s infinite;
}

/* ===== ИНТЕРАКТИВНЫЕ КЛАССЫ ===== */
.hover-lift {
    transition: transform 0.3s ease;
}

.hover-lift:hover {
    animation: hoverLift 0.3s ease forwards;
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    animation: hoverScale 0.3s ease forwards;
}

.button-press:active {
    animation: buttonPress 0.2s ease;
}

.icon-spin {
    animation: iconSpin 2s linear infinite;
}

.icon-bounce {
    animation: iconBounce 2s infinite;
}

/* ===== ЗАДЕРЖКИ АНИМАЦИЙ ===== */
.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}

.delay-400 {
    animation-delay: 0.4s;
}

.delay-500 {
    animation-delay: 0.5s;
}

/* ===== ПРОДОЛЖИТЕЛЬНОСТЬ АНИМАЦИЙ ===== */
.duration-fast {
    animation-duration: 0.3s;
}

.duration-normal {
    animation-duration: 0.6s;
}

.duration-slow {
    animation-duration: 1s;
}

.duration-slower {
    animation-duration: 1.5s;
}

/* ===== СПЕЦИАЛЬНЫЕ ЭФФЕКТЫ ===== */
.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% {
        content: '.';
    }
    40% {
        content: '..';
    }
    60%, 100% {
        content: '...';
    }
}

.typing-effect {
    overflow: hidden;
    border-right: 2px solid var(--eco-bright-green);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--eco-bright-green);
    }
}

/* ===== АДАПТИВНЫЕ АНИМАЦИИ ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (max-width: 768px) {
    .animate-fade-in-up,
    .animate-fade-in-down,
    .animate-fade-in-left,
    .animate-fade-in-right {
        animation-duration: 0.5s;
    }
    
    .eco-glow,
    .eco-text-glow {
        animation-duration: 2s;
    }
}
