/* ===== СОВРЕМЕННАЯ НАВИГАЦИЯ ===== */

/* ===== МОДЕРНИЗИРОВАННЫЙ HEADER ===== */
.modern-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--gradient-header);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.modern-navbar {
    padding: 0.75rem 0;
    min-height: 70px;
}

/* Логотип с анимацией */
.modern-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--text-white);
    transition: all 0.3s ease;
}

.modern-brand:hover {
    color: var(--text-white);
    text-decoration: none;
    transform: translateY(-1px);
}

.brand-logo-modern {
    width: 40px;
    height: 40px;
    background: var(--gradient-eco-bright);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    transition: all 0.3s ease;
}

.modern-brand:hover .brand-logo-modern {
    transform: rotate(360deg) scale(1.1);
    box-shadow: 0 6px 25px rgba(16, 185, 129, 0.5);
}

.brand-text-modern {
    font-size: 1.25rem;
    font-weight: 700;
    background: var(--gradient-eco-bright);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Навигация для авторизованных пользователей */
.auth-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    color: var(--text-white);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-button:hover::before {
    left: 100%;
}

.nav-button:hover {
    color: var(--text-white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
    border-color: var(--eco-bright-green);
    background: rgba(16, 185, 129, 0.2);
}

/* Специальные кнопки */
.nav-button.referrals {
    background: var(--gradient-eco-lime);
    border-color: var(--eco-lime-green);
}

.nav-button.referrals:hover {
    box-shadow: 0 8px 25px rgba(132, 204, 22, 0.4);
}

.nav-button.settings {
    background: var(--gradient-eco-emerald);
    border-color: var(--eco-emerald);
}

.nav-button.settings:hover {
    box-shadow: 0 8px 25px rgba(52, 211, 153, 0.4);
}

.nav-button.logout {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-color: #ef4444;
}

.nav-button.logout:hover {
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* SVG иконки */
.nav-icon {
    width: 18px;
    height: 18px;
    fill: currentColor;
    transition: all 0.3s ease;
}

.nav-button:hover .nav-icon {
    transform: scale(1.1);
}

/* Пользовательская информация */
.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: var(--gradient-eco-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.user-name {
    color: var(--text-white);
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1;
}

.user-balance {
    color: var(--eco-gold);
    font-weight: 700;
    font-size: 0.75rem;
    line-height: 1;
}

/* Мобильная навигация */
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-white);
    font-size: 1.5rem;
    padding: 0.5rem;
    border-radius: var(--border-radius-md);
    transition: all 0.3s ease;
}

.mobile-nav-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--eco-bright-green);
}

.mobile-nav {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--gradient-header);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.mobile-nav.show {
    display: block;
    animation: slideDown 0.3s ease;
}

.mobile-nav-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.mobile-nav .nav-button {
    justify-content: center;
    padding: 0.75rem 1rem;
}

/* Анимации */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(16, 185, 129, 0.6);
    }
}

.nav-button.glow {
    animation: glow 2s ease-in-out infinite;
}

/* Адаптивность */
@media (max-width: 991.98px) {
    .auth-nav {
        display: none;
    }
    
    .mobile-nav-toggle {
        display: block;
    }
    
    .user-info {
        margin-right: 1rem;
    }
    
    .user-details {
        display: none;
    }
}

@media (max-width: 575.98px) {
    .modern-navbar {
        padding: 0.5rem 0;
        min-height: 60px;
    }
    
    .brand-text-modern {
        font-size: 1.125rem;
    }
    
    .brand-logo-modern {
        width: 35px;
        height: 35px;
        font-size: 1.25rem;
    }
    
    .user-info {
        padding: 0.375rem 0.75rem;
    }
    
    .user-avatar {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
}

/* Эффекты при скролле */
.modern-header.scrolled {
    background: rgba(20, 83, 45, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
}

.modern-header.scrolled .brand-logo-modern {
    transform: scale(0.9);
}

/* Уведомления */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-red);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
