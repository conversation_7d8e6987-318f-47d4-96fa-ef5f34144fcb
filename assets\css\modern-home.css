/* ===== СОВРЕМЕННАЯ ГЛАВНАЯ СТРАНИЦА ===== */

/* ===== HERO СЕКЦИЯ ===== */
.modern-hero {
    background: var(--gradient-header);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.modern-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--eco-pattern-bg);
    opacity: 0.1;
    pointer-events: none;
}

.hero-content-modern {
    position: relative;
    z-index: 2;
}

.hero-badge-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    color: var(--text-white);
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    animation: badgeGlow 3s ease-in-out infinite;
}

.hero-title-modern {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--text-white);
    line-height: 1.1;
    margin-bottom: 1.5rem;
    animation: titleSlideIn 1s ease-out;
}

.hero-title-gradient {
    background: var(--gradient-nature);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

.hero-subtitle-modern {
    font-size: 1.25rem;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 2.5rem;
    max-width: 600px;
    animation: subtitleFadeIn 1s ease-out 0.3s both;
}

/* Статистика в hero */
.hero-stats-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card-modern {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    animation: statCardFloat 3s ease-in-out infinite;
}

.stat-card-modern:nth-child(2) {
    animation-delay: 0.5s;
}

.stat-card-modern:nth-child(3) {
    animation-delay: 1s;
}

.stat-card-modern:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
}

.stat-number-modern {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-white);
    margin-bottom: 0.5rem;
    background: var(--gradient-eco-bright);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label-modern {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Кнопки в hero */
.hero-buttons-modern {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    animation: buttonsSlideUp 1s ease-out 0.6s both;
}

.btn-hero-primary {
    background: var(--gradient-eco-bright);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-hero-primary:hover::before {
    left: 100%;
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
    color: white;
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--eco-bright-green);
    transform: translateY(-3px);
    color: var(--text-white);
}

/* ===== СЕКЦИИ КОНТЕНТА ===== */
.content-section-modern {
    padding: 5rem 0;
    background: var(--primary-white);
    position: relative;
}

.content-section-modern.eco-bg {
    background: var(--primary-light-green);
}

.content-section-modern.gradient-bg {
    background: linear-gradient(135deg, var(--primary-white) 0%, var(--primary-light-green-50) 100%);
}

.section-title-modern {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.section-title-eco {
    background: var(--gradient-eco-bright);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle-modern {
    font-size: 1.1rem;
    color: var(--text-dark-medium);
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== КАРТОЧКИ ФУНКЦИЙ ===== */
.features-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card-modern {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-eco-bright);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card-modern:hover::before {
    transform: scaleX(1);
}

.feature-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.15);
    border-color: var(--eco-bright-green);
}

.feature-icon-modern {
    width: 80px;
    height: 80px;
    background: var(--gradient-eco-bright);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all 0.3s ease;
}

.feature-card-modern:hover .feature-icon-modern {
    transform: scale(1.1) rotate(360deg);
}

.feature-title-modern {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.feature-description-modern {
    color: var(--text-dark-medium);
    line-height: 1.6;
}

/* ===== АНИМАЦИИ ===== */
@keyframes badgeGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(16, 185, 129, 0.6);
    }
}

@keyframes titleSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes subtitleFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes buttonsSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes statCardFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* ===== АДАПТИВНОСТЬ ===== */
@media (max-width: 991.98px) {
    .hero-title-modern {
        font-size: 2.5rem;
    }
    
    .hero-subtitle-modern {
        font-size: 1.1rem;
    }
    
    .hero-buttons-modern {
        justify-content: center;
    }
}

@media (max-width: 767.98px) {
    .modern-hero {
        padding-top: 60px;
        min-height: 90vh;
    }
    
    .hero-title-modern {
        font-size: 2rem;
    }
    
    .hero-subtitle-modern {
        font-size: 1rem;
    }
    
    .hero-stats-modern {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .content-section-modern {
        padding: 3rem 0;
    }
    
    .section-title-modern {
        font-size: 2rem;
    }
    
    .features-grid-modern {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 575.98px) {
    .hero-title-modern {
        font-size: 1.75rem;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }

    .hero-buttons-modern {
        flex-direction: column;
        align-items: center;
    }

    .feature-card-modern {
        padding: 1.5rem;
    }

    .feature-icon-modern {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ АНИМАЦИИ ===== */
@keyframes textGlow {
    0%, 100% {
        text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
    }
    50% {
        text-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.text-glow-animate {
    animation: textGlow 3s ease-in-out infinite;
}

.bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.slide-in-left {
    animation: slideInFromLeft 0.8s ease-out;
}

.slide-in-right {
    animation: slideInFromRight 0.8s ease-out;
}
