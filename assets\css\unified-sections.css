/* ===== УНИФИКАЦИЯ И ВЫРАВНИВАНИЕ ВСЕХ СЕКЦИЙ ===== */

/* Переменные для единообразия */
:root {
    --section-padding: 5rem 0;
    --section-padding-mobile: 3rem 0;
    --card-gap: 2rem;
    --card-gap-mobile: 1.5rem;
    --card-padding: 2rem;
    --card-padding-mobile: 1.5rem;
    --border-radius-card: 1rem;
    --transition-standard: all 0.3s ease;
}

/* ===== УНИФИКАЦИЯ ВСЕХ СЕКЦИЙ ===== */

/* Базовые стили для всех секций */
section,
.content-section-modern,
.features-section,
.how-it-works-section,
.partners-section,
.cta-section {
    padding: var(--section-padding) !important;
    position: relative;
    overflow: hidden;
}

/* Контейнеры секций */
section .container,
.content-section-modern .container,
.features-section .container,
.how-it-works-section .container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* ===== УНИФИКАЦИЯ СЕТОК И КАРТОЧЕК ===== */

/* Единая сетка для всех карточек */
.features-grid-modern,
.row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--card-gap);
    margin-top: 2rem;
}

/* Специальные сетки для разных типов контента */
.row.investment-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.row.package-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.row.feature-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.row.step-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* ===== УНИФИКАЦИЯ КАРТОЧЕК ===== */

/* Базовые стили для всех карточек */
.feature-card-modern,
.investment-card,
.luxury-investment-card,
.feature-card,
.step-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: var(--border-radius-card);
    padding: var(--card-padding);
    transition: var(--transition-standard);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Эффекты при наведении */
.feature-card-modern:hover,
.investment-card:hover,
.luxury-investment-card:hover,
.feature-card:hover,
.step-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #10b981;
}

/* ===== УНИФИКАЦИЯ ЗАГОЛОВКОВ ===== */

/* Заголовки секций */
.section-title,
.section-title-modern,
h2 {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    line-height: 1.2;
}

/* Подзаголовки секций */
.section-subtitle,
.section-subtitle-modern {
    font-size: 1.1rem;
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Заголовки карточек */
.feature-title-modern,
.investment-title,
.luxury-investment-title,
.feature-title,
.step-title,
h3, h4, h5 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.3;
}

/* ===== УНИФИКАЦИЯ ИКОНОК ===== */

/* Контейнеры иконок */
.feature-icon-modern,
.feature-icon,
.step-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    transition: var(--transition-standard);
}

.feature-icon-modern:hover,
.feature-icon:hover,
.step-icon:hover {
    transform: scale(1.1) rotate(360deg);
}

/* ===== УНИФИКАЦИЯ КНОПОК ===== */

/* Базовые стили кнопок */
.btn-luxury-primary,
.btn-hero-primary,
.btn-primary,
.invest-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: var(--transition-standard);
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
    min-width: 180px;
    min-height: 50px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-luxury-primary:hover,
.btn-hero-primary:hover,
.btn-primary:hover,
.invest-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: #ffffff;
    text-decoration: none;
}

/* ===== УНИФИКАЦИЯ ИЗОБРАЖЕНИЙ ===== */

/* Изображения в карточках */
.investment-image img,
.feature-image img,
img {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    object-fit: cover;
}

/* Контейнеры изображений */
.investment-image,
.feature-image {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

/* ===== АДАПТИВНОСТЬ ===== */

@media (max-width: 1024px) {
    .features-grid-modern,
    .row {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }
    
    .row.package-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (max-width: 768px) {
    :root {
        --section-padding: var(--section-padding-mobile);
        --card-gap: var(--card-gap-mobile);
        --card-padding: var(--card-padding-mobile);
    }
    
    .features-grid-modern,
    .row {
        grid-template-columns: 1fr;
        gap: var(--card-gap-mobile);
    }
    
    .section-title,
    .section-title-modern {
        font-size: 2rem;
    }
    
    .feature-icon-modern,
    .feature-icon,
    .step-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .feature-card-modern,
    .investment-card,
    .luxury-investment-card,
    .feature-card,
    .step-card {
        padding: 1.25rem;
    }
    
    .section-title,
    .section-title-modern {
        font-size: 1.75rem;
    }
    
    .btn-luxury-primary,
    .btn-hero-primary,
    .btn-primary,
    .invest-btn {
        min-width: 150px;
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}
