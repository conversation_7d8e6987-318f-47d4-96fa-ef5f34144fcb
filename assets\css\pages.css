/* ===== СТИЛИ СТРАНИЦ ===== */

/* ===== ГЛАВНАЯ СТРАНИЦА ===== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: var(--gradient-primary);
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(22, 163, 74, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(22, 163, 74, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(26, 61, 46, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: rgba(212, 175, 55, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--border-radius-xl);
    color: var(--accent-gold);
    font-size: var(--font-size-sm);
    font-weight: 500;
    backdrop-filter: blur(5px);
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--text-white);
}

.text-gradient {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-light-gray);
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 600px;
}

.hero-stats {
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(212, 175, 55, 0.4);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-light-gray);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* ===== СТРАНИЦЫ АВТОРИЗАЦИИ ===== */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--primary-light);
    position: relative;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(22, 163, 74, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(37, 99, 235, 0.05) 0%, transparent 50%);
    z-index: 1;
}

.auth-card {
    background: var(--primary-white);
    border: 1px solid var(--primary-gray-200);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    position: relative;
    z-index: 2;
}

.auth-header {
    padding: 2rem 2rem 1rem;
    text-align: center;
    border-bottom: 1px solid var(--primary-gray-200);
    background: var(--primary-light);
}

.auth-header h3 {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.auth-header p {
    color: var(--text-dark-medium);
    margin: 0;
}

.auth-body {
    padding: 2rem;
}

.auth-footer {
    padding: 1rem 2rem 2rem;
    text-align: center;
    border-top: 1px solid var(--primary-gray-200);
    background: var(--primary-light);
}

.auth-links {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
    font-size: var(--font-size-sm);
}

.auth-links a {
    color: var(--accent-green);
    text-decoration: none;
    transition: var(--transition-fast);
}

.auth-links a:hover {
    color: var(--accent-green-dark);
    text-decoration: underline;
}

/* ===== DASHBOARD ===== */
.welcome-header {
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.welcome-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(212, 175, 55, 0.1));
    z-index: 1;
}

.welcome-title {
    color: var(--text-white);
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.welcome-subtitle {
    color: var(--text-light-gray);
    margin: 0;
    position: relative;
    z-index: 2;
}

.wave {
    display: inline-block;
    animation: wave 2s infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(20deg); }
    75% { transform: rotate(-10deg); }
}

.stats-card {
    background: var(--primary-white);
    border: 1px solid var(--primary-light-green-100);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--eco-bright-green);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-eco-bright);
}

.stats-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-light-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: var(--eco-forest-green);
    font-size: 1.25rem;
}

.stats-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.stats-label {
    color: var(--text-green-medium);
    font-size: var(--font-size-sm);
    margin-bottom: 0.75rem;
}

.stats-change {
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stats-change.positive {
    color: var(--success-green);
}

.stats-change.negative {
    color: var(--danger-red);
}

.stats-action {
    margin-top: 1rem;
}

/* ===== INVESTMENT CARDS ===== */
.investment-card {
    background: rgba(26, 26, 26, 0.9);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.investment-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(212, 175, 55, 0.4);
}

.investment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.investment-title {
    color: var(--text-white);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.investment-type {
    color: var(--accent-gold);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.investment-status {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background: rgba(34, 197, 94, 0.2);
    color: var(--success-green);
}

.status-completed {
    background: rgba(22, 163, 74, 0.2);
    color: var(--info-green);
}

.status-pending {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-yellow);
}

.investment-amount {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 1rem;
}

.investment-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.detail-item {
    text-align: center;
}

.detail-label {
    color: var(--text-light-gray);
    font-size: var(--font-size-xs);
    margin-bottom: 0.25rem;
}

.detail-value {
    color: var(--text-white);
    font-weight: 600;
}

.investment-progress {
    margin-bottom: 1rem;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: var(--font-size-sm);
}

.progress-label .label {
    color: var(--text-light-gray);
}

.progress-label .value {
    color: var(--accent-gold);
    font-weight: 600;
}

/* ===== ДОПОЛНИТЕЛЬНЫЕ СТИЛИ ДЛЯ ГЛАВНОЙ СТРАНИЦЫ ===== */
.eco-icon {
    display: inline-block;
    margin: 1rem 0;
}

.partners-section {
    background: rgba(22, 163, 74, 0.1);
    border-top: 1px solid rgba(22, 163, 74, 0.2);
    border-bottom: 1px solid rgba(22, 163, 74, 0.2);
}

.partner-logo {
    padding: 1rem 2rem;
    background: rgba(26, 26, 26, 0.8);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: var(--border-radius-md);
    color: var(--text-light-gray);
    font-weight: 500;
    transition: var(--transition-normal);
    backdrop-filter: blur(5px);
}

.partner-logo:hover {
    color: var(--accent-gold);
    border-color: rgba(212, 175, 55, 0.4);
    transform: translateY(-2px);
}

/* ===== FEATURES SECTION ===== */
.features-section {
    padding: 5rem 0;
    background: rgba(26, 61, 46, 0.1);
}

.feature-card {
    background: rgba(26, 26, 26, 0.9);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: rgba(212, 175, 55, 0.4);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--accent-gold);
    font-size: 1.5rem;
    transition: var(--transition-normal);
}

.feature-card:hover .feature-icon {
    background: var(--accent-gold);
    color: var(--primary-black);
    transform: scale(1.1);
}

.feature-title {
    color: var(--text-white);
    font-weight: 600;
    margin-bottom: 1rem;
}

.feature-description {
    color: var(--text-light-gray);
    line-height: 1.6;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        flex-direction: column;
    }

    .auth-card {
        margin: 1rem;
    }

    .auth-header,
    .auth-body,
    .auth-footer {
        padding: 1.5rem;
    }

    .welcome-header {
        padding: 1.5rem;
    }

    .welcome-title {
        font-size: var(--font-size-2xl);
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .investment-details {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .footer-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .live-chat-widget {
        bottom: 1rem;
        left: 1rem;
    }

    .chat-window {
        width: calc(100vw - 2rem);
        max-width: 300px;
    }

    .btn-back-to-top {
        bottom: 1rem;
        right: 1rem;
    }

    .partner-logo {
        padding: 0.75rem 1.5rem;
        font-size: var(--font-size-sm);
    }

    .feature-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .feature-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }
}
